package com.carsaver.partner.configuration.graphics.mappers

import com.carsaver.partner.configuration.graphics.GraphicsConfigRequest
import com.fasterxml.jackson.databind.ObjectMapper
import java.util.*

class GraphicsConfigPayloadMapper {

    companion object {
        val mapper = ObjectMapper()

        fun mapToConfig(payload: Map<String, String?>): GraphicsConfigRequest {
            val graphicsConfig = GraphicsConfigRequest()

            // Map logo properties
            payload["logoFile"]?.let {
                if (it == "null") {
                    graphicsConfig.logoName = Optional.empty()
                } else {
                    graphicsConfig.logoName = Optional.of(it)
                }
            }

            // Map favicon properties
            payload["faviconFile"]?.let {
                if (it == "null") {
                    graphicsConfig.favIconName = Optional.empty()
                } else {
                    graphicsConfig.favIconName = Optional.of(it)
                }
            }

            if (payload["dealershipFile1"] != null || payload["dealershipFile2"] != null) {
                val dealershipImages = GraphicsConfigRequest.DealershipImages()
                payload["dealershipFile1"]?.let {
                    if (it == "null") {
                        dealershipImages.mobileName = Optional.empty()
                    } else {
                        dealershipImages.mobileName = Optional.of(it)
                    }
                }
                payload["dealershipFile2"]?.let {
                    if (it == "null") {
                        dealershipImages.desktopName = Optional.empty()
                    } else {
                        dealershipImages.desktopName = Optional.of(it)
                    }
                }

                graphicsConfig.dealershipImages = Optional.of(dealershipImages)
            }

            return graphicsConfig
        }
    }
}

package com.carsaver.partner.configuration.graphics.mappers

import com.carsaver.partner.configuration.graphics.GraphicsConfigRequest
import com.fasterxml.jackson.databind.ObjectMapper
import java.util.*

class GraphicsConfigPayloadMapper {

    companion object {
        val mapper = ObjectMapper()

        fun mapToConfig(payload: Map<String, String?>): GraphicsConfigRequest {
            val graphicsConfig = GraphicsConfigRequest()

            // Map logo properties
            payload["logoFile"]?.let {
                if (it == "null") {
                    graphicsConfig.logoName = Optional.empty()
                } else {
                    graphicsConfig.logoName = Optional.of(it)
                }
            }

            // Map favicon properties
            payload["faviconFile"]?.let {
                if (it == "null") {
                    graphicsConfig.favIconName = Optional.empty()
                } else {
                    graphicsConfig.favIconName = Optional.of(it)
                }
            }

//            // Map dealership images properties
//            payload["dealershipFile1"]?.let { graphicsConfig.dealershipImages?.mobileName = Optional.of(it) }
//            payload["dealershipFile2"]?.let { graphicsConfig.dealershipImages?.desktopName = Optional.of(it) }
//
//            graphicsConfig.dealershipImages = Optional.of(GraphicsConfigRequest.DealershipImages(
//                mobileName = Optional.of(payload["dealershipFile1"]),
//                desktopName = payload["dealershipFile2"]
//            ))

            return graphicsConfig
        }
    }
}
